#ifndef __LOGIC_H__
#define __LOGIC_H__

#include "stdint.h"
#include "gd32f4xx_rtc.h"

#ifdef __cplusplus
extern "C" {
#endif

/*************************************************************************************************/
/* Flash地址定义 */
/*************************************************************************************************/
#define DEVICE_ID_FLASH_ADDR        0x000000    // 设备ID存储地址
#define SYSTEM_PARAMS_FLASH_ADDR    0x001000    // 系统参数存储地址（4KB偏移）
#define FLASH_TEST_ADDR             0x002000    // Flash测试地址（8KB偏移）
#define BOOT_COUNT_FLASH_ADDR       0x003000    // 上电次数存储地址（12KB偏移）
#define FLASH_LOG_ADDR              0x004000    // Flash日志存储地址（16KB偏移）

/*************************************************************************************************/
/* 系统状态管理 */
/*************************************************************************************************/
typedef enum {
    SYSTEM_IDLE = 0,        // 系统空闲状态
    SYSTEM_SAMPLING = 1     // 采样模式状态
} system_state_t;

typedef enum {
    SAMPLING_MODE_NORMAL = 0,   // 普通采样模式（sample文件夹）
    SAMPLING_MODE_HIDE = 1      // 隐藏采样模式（hideData文件夹）
} sampling_mode_t;

/*************************************************************************************************/
/* RTC时间转换辅助结构体 */
/*************************************************************************************************/
typedef struct {
    uint8_t year_dec;
    uint8_t month_dec;
    uint8_t day_dec;
    uint8_t hour_dec;
    uint8_t minute_dec;
    uint8_t second_dec;
} rtc_decimal_time_t;

/*************************************************************************************************/
/* RTC配置状态管理 */
/*************************************************************************************************/
typedef enum {
    RTC_CONFIG_IDLE = 0,        // RTC配置空闲状态
    RTC_CONFIG_WAITING = 1      // 等待用户输入时间
} rtc_config_state_t;

/*************************************************************************************************/
/* Ratio配置状态管理 */
/*************************************************************************************************/
typedef enum {
    RATIO_CONFIG_IDLE = 0,      // Ratio配置空闲状态
    RATIO_CONFIG_WAITING = 1    // 等待用户输入ratio值
} ratio_config_state_t;

/*************************************************************************************************/
/* Limit配置状态管理 */
/*************************************************************************************************/
typedef enum {
    LIMIT_CONFIG_IDLE = 0,      // Limit配置空闲状态
    LIMIT_CONFIG_WAITING = 1    // 等待用户输入limit值
} limit_config_state_t;

// 全局系统状态变量
extern system_state_t g_system_state;
extern sampling_mode_t g_sampling_mode;
extern rtc_config_state_t g_rtc_config_state;
extern ratio_config_state_t g_ratio_config_state;
extern limit_config_state_t g_limit_config_state;

/*************************************************************************************************/
/* 系统参数变量 */
/*************************************************************************************************/
extern float g_ratio;      // 变比
extern float g_limit;      // ADC_01的阈值
extern uint16_t g_period;  // 周期

/*************************************************************************************************/
/* 采样模式数据变量 */
/*************************************************************************************************/
extern float g_adc_01;     // ADC_01计算值，只在dataread01事件时更新
extern uint8_t g_oled_refresh_flag;  // OLED刷新标志，只在dataread01事件时设置
extern uint8_t g_rtc_display_flag;   // RTC时间显示刷新标志，20Hz更新

/*************************************************************************************************/
/* 设备ID管理功能函数 */
/*************************************************************************************************/
void device_id_init(void);
void device_id_read_from_flash(void);
void device_id_write_to_flash(void);

/*************************************************************************************************/
/* 系统初始化功能函数 */
/*************************************************************************************************/
void system_startup_init(void);

/*************************************************************************************************/
/* 系统自检功能函数 */
/*************************************************************************************************/
void system_selftest(void);

/*************************************************************************************************/
/* RTC配置功能函数 */
/*************************************************************************************************/
void rtc_config_start(void);
void rtc_config_set_time(const char* datetime_str);
void rtc_show_current_time(void);
uint8_t parse_datetime_string(const char* datetime_str, rtc_parameter_struct* rtc_param);

/*************************************************************************************************/
/* Ratio配置功能函数 */
/*************************************************************************************************/
void ratio_config_start(void);
void ratio_config_set_value(const char* value_str);

/*************************************************************************************************/
/* Limit配置功能函数 */
/*************************************************************************************************/
void limit_config_start(void);
void limit_config_set_value(const char* value_str);

/*************************************************************************************************/
/* 参数保存功能函数 */
/*************************************************************************************************/
void config_save_to_flash(void);
void config_read_from_flash(void);

/*************************************************************************************************/
/* 系统参数管理功能函数 */
/*************************************************************************************************/
void system_params_init(void);
void system_params_write_to_flash(void);

/*************************************************************************************************/
/* 文件系统管理功能函数 */
/*************************************************************************************************/
void filesystem_init(void);
uint8_t tf_card_real_test(void);
uint8_t flash_real_test(void);

/*************************************************************************************************/
/* 采样模式管理功能函数 */
/*************************************************************************************************/
void sampling_mode_init(void);
void sampling_mode_enter(void);
void sampling_mode_exit(void);
void sampling_mode_task(void);
void sampling_rtc_display_task(void);
void dataread01_event_handler(void);
void overlimit01_event_handler(void);
void period_change_and_save(uint16_t new_period);

/*************************************************************************************************/
/* 数据记录功能函数 */
/*************************************************************************************************/
void data_logging_init(void);
void data_logging_write_record_with_time(rtc_parameter_struct* rtc_time, rtc_decimal_time_t* decimal_time);
void overlimit_data_write_record(rtc_parameter_struct* rtc_time, rtc_decimal_time_t* decimal_time);

/*************************************************************************************************/
/* Hide模式功能函数 */
/*************************************************************************************************/
void hide_mode_enter(void);
void hide_mode_exit(void);
void hide_data_encrypt(uint32_t unix_timestamp, float adc_value, char* encrypted_data);
uint32_t rtc_to_unix_timestamp(rtc_parameter_struct* rtc_time);

/*************************************************************************************************/
/* RTC时间转换辅助函数 */
/*************************************************************************************************/
void rtc_bcd_to_decimal(rtc_parameter_struct* rtc_time, rtc_decimal_time_t* decimal_time);

/*************************************************************************************************/
/* 日志系统功能函数 */
/*************************************************************************************************/
void boot_count_read_from_flash(void);
void boot_count_write_to_flash(void);
void boot_count_init(void);
void boot_count_reset(void);
void log_system_init(void);
void log_write_event(uint8_t event_index, const char* additional_info);

/*************************************************************************************************/
/* Flash日志存储功能函数 */
/*************************************************************************************************/
void flash_log_flag_read(void);
void flash_log_init_for_new_session(void);
void flash_log_reset_flag(void);
void flash_log_clear(void);
void flash_log_write_event(uint8_t event_index, const char* additional_info);
void flash_log_transfer_to_tf(void);

/*************************************************************************************************/
/* Config.ini文件读取功能函数 */
/*************************************************************************************************/
uint8_t parse_config_content(const char* file_content, uint32_t file_size, float* ratio, float* limit);
uint8_t read_config_ini(float* ratio, float* limit);
void config_ini_process(void);

/*************************************************************************************************/
/* ADC应用程序功能函数 */
/*************************************************************************************************/
void adc_task(void);

/*************************************************************************************************/
/* 按键应用程序功能函数 */
/*************************************************************************************************/
typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_3,
    USER_BUTTON_4,
    USER_BUTTON_5,
    USER_BUTTON_6,
    USER_BUTTON_MAX,
} user_button_t;

void app_btn_init(void);
void btn_task(void);

/*************************************************************************************************/
/* LED应用程序功能函数 */
/*************************************************************************************************/
void led_task(void);

/*************************************************************************************************/
/* OLED应用程序功能函数 */
/*************************************************************************************************/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...);
void oled_task(void);

/*************************************************************************************************/
/* RTC应用程序功能函数 */
/*************************************************************************************************/
void rtc_task(void);

/*************************************************************************************************/
/* SD卡应用程序功能函数 */
/*************************************************************************************************/
void sd_fatfs_init(void);

/*************************************************************************************************/
/* USART应用程序功能函数 */
/*************************************************************************************************/
int my_printf(uint32_t usart_periph, const char *format, ...);
void uart_task(void);

#ifdef __cplusplus
}
#endif

#endif /* __LOGIC_H__ */