#include "configuration.h"
#include "logic.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>

/*************************************************************************************************/
/* 全局变量定义 */
/*************************************************************************************************/
system_state_t g_system_state = SYSTEM_IDLE;  // 系统状态，默认为空闲状态
sampling_mode_t g_sampling_mode = SAMPLING_MODE_NORMAL;  // 采样模式，默认为普通模式
rtc_config_state_t g_rtc_config_state = RTC_CONFIG_IDLE;  // RTC配置状态，默认为空闲
ratio_config_state_t g_ratio_config_state = RATIO_CONFIG_IDLE;  // Ratio配置状态，默认为空闲
limit_config_state_t g_limit_config_state = LIMIT_CONFIG_IDLE;  // Limit配置状态，默认为空闲

// 系统参数变量（初始值将从Flash读取）
float g_ratio = 0.0f;      // 变比，从Flash读取
float g_limit = 0.0f;      // ADC_01的阈值，从Flash读取
uint16_t g_period = 0;     // 周期，从Flash读取

// 采样模式数据变量
float g_adc_01 = 0.0f;     // ADC_01计算值，只在dataread01事件时更新
uint8_t g_oled_refresh_flag = 0;  // OLED刷新标志，只在dataread01事件时设置
uint8_t g_rtc_display_flag = 0;   // RTC时间显示刷新标志，20Hz更新

/*************************************************************************************************/
/* 外部变量声明 */
/*************************************************************************************************/
extern rtc_parameter_struct rtc_initpara;  // RTC参数结构体
extern uint16_t prescaler_a, prescaler_s;  // RTC分频器参数
extern FATFS fs;                           // 文件系统对象
extern uint16_t adc_value[1];              // ADC采样值数组
extern uint8_t ucLed[6];                   // LED状态数组

/*************************************************************************************************/
/* Flash存储布局定义 */
/*************************************************************************************************/
// Flash扇区大小：4KB (0x1000字节)
// Flash存储布局：
// 0x0000-0x0FFF: 设备ID存储区域 (4KB扇区)
// 0x1000-0x1FFF: 系统参数存储区域 (4KB扇区)
// 0x2000-0x2FFF: Flash测试区域 (4KB扇区)
// 0x3000-0x3FFF: 上电次数存储区域 (4KB扇区)
// 0x4000-0x4FFF: Flash日志存储区域 (4KB扇区)

// 设备ID相关定义
#define DEVICE_ID_STRING        "Device_ID:2025-CIMC-2025271144" // 设备ID字符串
#define DEVICE_ID_MAX_LEN       64                              // 设备ID最大长度

static char device_id_buffer[DEVICE_ID_MAX_LEN] = {0};         // 设备ID缓冲区

/*************************************************************************************************/
/* Flash日志存储相关定义 */
/*************************************************************************************************/
#define FLASH_LOG_FLAG_ADDR         (FLASH_LOG_ADDR)                    // Flash日志标志位地址
#define FLASH_LOG_DATA_ADDR         (FLASH_LOG_ADDR + 4)                // Flash日志数据起始地址
#define FLASH_LOG_MAX_SIZE          (0x1000 - 4)                        // Flash日志最大存储大小(4KB-4字节标志位)
#define FLASH_LOG_MAX_EVENTS        20                                   // 最大存储事件数量

static uint8_t flash_log_flag = 0;                                      // Flash日志标志位(log_01)
static uint32_t flash_log_write_offset = 0;                             // Flash日志写入偏移量

// 系统参数Flash存储相关定义
#define SYSTEM_PARAMS_MAGIC         0x12345678                  // 魔数，用于验证数据有效性
#define SYSTEM_PARAMS_VERSION       0x20250617                  // 程序版本标识，每次下载程序时更新

// 系统参数存储结构体
typedef struct {
    uint32_t magic;         // 魔数 (4字节)
    uint32_t version;       // 程序版本标识 (4字节)
    float ratio;            // 变比 (4字节)
    float limit;            // ADC_01的阈值 (4字节)
    uint16_t period;        // 周期 (2字节)
    uint16_t reserved;      // 保留字段，用于对齐 (2字节)
    uint32_t checksum;      // 校验和 (4字节)
} system_params_flash_t;    // 总计：24字节

// 注意：结构体大小现在为24字节，limit改为float类型

/*************************************************************************************************/
/* 设备ID管理功能 */
/*************************************************************************************************/

/**
 * @brief 设备ID初始化函数
 * 检查Flash中是否已存储设备ID，如果没有则写入默认ID
 */
void device_id_init(void)
{
    // 先尝试从Flash读取设备ID
    device_id_read_from_flash();

    // 检查读取的ID是否有效（非空且包含预期内容）
    if (strlen(device_id_buffer) == 0 || strstr(device_id_buffer, "Device_ID:") == NULL) {
        // Flash中没有有效的设备ID，写入默认ID
        device_id_write_to_flash();
        // 重新读取确认写入成功
        device_id_read_from_flash();
    }
}

/**
 * @brief 从Flash读取设备ID
 */
void device_id_read_from_flash(void)
{
    // 清空缓冲区
    memset(device_id_buffer, 0, DEVICE_ID_MAX_LEN);

    // 从Flash读取设备ID
    spi_flash_buffer_read((uint8_t*)device_id_buffer, DEVICE_ID_FLASH_ADDR, DEVICE_ID_MAX_LEN);

    // 确保字符串以null结尾
    device_id_buffer[DEVICE_ID_MAX_LEN - 1] = '\0';
}

/**
 * @brief 将设备ID写入Flash
 */
void device_id_write_to_flash(void)
{
    // 清空缓冲区并复制设备ID字符串
    memset(device_id_buffer, 0, DEVICE_ID_MAX_LEN);
    strncpy(device_id_buffer, DEVICE_ID_STRING, DEVICE_ID_MAX_LEN - 1);

    // 擦除对应扇区（4KB扇区，地址0x1000在第一个扇区内）
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);

    // 写入设备ID到Flash
    spi_flash_buffer_write((uint8_t*)device_id_buffer, DEVICE_ID_FLASH_ADDR, DEVICE_ID_MAX_LEN);
}

/*************************************************************************************************/
/* 系统初始化功能 */
/*************************************************************************************************/

/**
 * @brief 系统启动初始化函数
 * 负责系统启动时的串口输出和设备ID管理
 */
void system_startup_init(void)
{
    // 系统启动信息
    my_printf(DEBUG_USART, "====system init====\r\n");

    // 初始化设备ID（从Flash读取或写入默认值）
    device_id_init();

    // 输出设备ID
    my_printf(DEBUG_USART, "%s\r\n", device_id_buffer);

    // 系统就绪信息
    my_printf(DEBUG_USART, "====system ready====\r\n");

    // 设置系统状态为空闲
    g_system_state = SYSTEM_IDLE;
}

/*************************************************************************************************/
/* 系统自检功能 */
/*************************************************************************************************/

/**
 * @brief 系统自检函数
 * 检测Flash、TF卡、RTC等硬件状态并输出结果
 */
void system_selftest(void)
{
    uint32_t flash_id = 0;
    uint32_t tf_capacity = 0;
    uint8_t flash_test_result = 0;
    uint8_t tf_test_result = 0;

    my_printf(DEBUG_USART, "======system selftest======\r\n");
    log_write_event(4, NULL); // 系统硬件测试开始

    // 1. Flash真正的读写测试
    flash_test_result = flash_real_test();
    if (flash_test_result) {
        my_printf(DEBUG_USART, "flash............ok\r\n");
    } else {
        my_printf(DEBUG_USART, "flash............error\r\n");
        log_write_event(7, NULL); // Flash测试失败
    }

    // 2. TF卡真正的读写测试
    tf_test_result = tf_card_real_test();
    if (tf_test_result) {
        my_printf(DEBUG_USART, "TF card............ok\r\n");
        // 获取TF卡容量
        tf_capacity = sd_card_capacity_get();
    } else {
        my_printf(DEBUG_USART, "TF card............error\r\n");
        tf_capacity = 0;
        log_write_event(6, NULL); // TF卡测试失败
    }

    // 3. 读取Flash ID
    flash_id = spi_flash_read_id();
    my_printf(DEBUG_USART, "flash ID: 0x%lX\r\n", flash_id);

    // 4. 输出TF卡容量或错误信息
    if (tf_test_result) {
        my_printf(DEBUG_USART, "TF card memory: %ld KB\r\n", tf_capacity);
    } else {
        my_printf(DEBUG_USART, "can not find TF card\r\n");
    }

    // 5. 输出RTC时间
    rtc_current_time_get(&rtc_initpara);
    my_printf(DEBUG_USART, "RTC: 20%02x-%02x-%02x %02x:%02x:%02x\r\n",
              rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
              rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);

    // 6. 记录测试结果日志
    if (flash_test_result && tf_test_result) {
        log_write_event(5, NULL); // 测试成功
    }

    my_printf(DEBUG_USART, "======system selftest======\r\n");
}

/*************************************************************************************************/
/* RTC配置功能 */
/*************************************************************************************************/

/**
 * @brief 开始RTC配置流程
 */
void rtc_config_start(void)
{
    g_rtc_config_state = RTC_CONFIG_WAITING;
    my_printf(DEBUG_USART, "Input Datetime\r\n");
    log_write_event(1, NULL); // RTC配置开始
}

/**
 * @brief 显示当前RTC时间
 */
void rtc_show_current_time(void)
{
    rtc_current_time_get(&rtc_initpara);
    my_printf(DEBUG_USART, "Current Time:20%02x-%02x-%02x %02x:%02x:%02x\r\n",
              rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
              rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}

/**
 * @brief 解析日期时间字符串并转换为BCD格式
 * @param datetime_str 输入的日期时间字符串，格式："2025-01-01 12:00:30"
 * @param rtc_param 输出的RTC参数结构体
 * @return 解析成功返回1，失败返回0
 */
uint8_t parse_datetime_string(const char* datetime_str, rtc_parameter_struct* rtc_param)
{
    int year, month, day, hour, minute, second;

    // 解析字符串格式："2025-01-01 12:00:30"
    if (sscanf(datetime_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second) != 6) {
        return 0; // 解析失败
    }

    // 验证范围
    if (year < 2000 || year > 2099 || month < 1 || month > 12 ||
        day < 1 || day > 31 || hour < 0 || hour > 23 ||
        minute < 0 || minute > 59 || second < 0 || second > 59) {
        return 0; // 范围错误
    }

    // 转换为BCD格式
    rtc_param->year = ((year - 2000) / 10) * 16 + ((year - 2000) % 10);  // 年份转换为BCD
    rtc_param->date = (day / 10) * 16 + (day % 10);                      // 日期转换为BCD
    rtc_param->hour = (hour / 10) * 16 + (hour % 10);                    // 小时转换为BCD
    rtc_param->minute = (minute / 10) * 16 + (minute % 10);              // 分钟转换为BCD
    rtc_param->second = (second / 10) * 16 + (second % 10);              // 秒转换为BCD

    // 月份使用RTC库定义的常量
    switch (month) {
        case 1:  rtc_param->month = RTC_JAN; break;
        case 2:  rtc_param->month = RTC_FEB; break;
        case 3:  rtc_param->month = RTC_MAR; break;
        case 4:  rtc_param->month = RTC_APR; break;
        case 5:  rtc_param->month = RTC_MAY; break;
        case 6:  rtc_param->month = RTC_JUN; break;
        case 7:  rtc_param->month = RTC_JUL; break;
        case 8:  rtc_param->month = RTC_AUG; break;
        case 9:  rtc_param->month = RTC_SEP; break;
        case 10: rtc_param->month = RTC_OCT; break;
        case 11: rtc_param->month = RTC_NOV; break;
        case 12: rtc_param->month = RTC_DEC; break;
        default: return 0; // 无效月份
    }

    // 设置其他必要参数
    rtc_param->display_format = RTC_24HOUR;
    rtc_param->am_pm = RTC_AM;
    rtc_param->day_of_week = RTC_MONDAY; // 简化处理，设为周一

    return 1; // 解析成功
}

/**
 * @brief 设置RTC时间
 * @param datetime_str 日期时间字符串
 */
void rtc_config_set_time(const char* datetime_str)
{
    rtc_parameter_struct new_rtc_param;

    // 解析日期时间字符串
    if (parse_datetime_string(datetime_str, &new_rtc_param) == 0) {
        my_printf(DEBUG_USART, "RTC Config failed - Invalid format\r\n");
        log_write_event(3, NULL); // RTC配置失败
        g_rtc_config_state = RTC_CONFIG_IDLE;
        return;
    }

    // 设置分频器参数（使用全局分频器参数）
    new_rtc_param.factor_asyn = prescaler_a;
    new_rtc_param.factor_syn = prescaler_s;

    // 更新RTC时间
    if (rtc_init(&new_rtc_param) == SUCCESS) {
        my_printf(DEBUG_USART, "RTC Config success\r\n");
        // 显示设置后的时间
        rtc_current_time_get(&rtc_initpara);
        my_printf(DEBUG_USART, "Time:20%02x-%02x-%02x %02x:%02x:%02x\r\n",
                  rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
                  rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
        // 记录RTC配置成功日志
        log_write_event(2, datetime_str); // RTC配置成功，附加时间信息
    } else {
        my_printf(DEBUG_USART, "RTC Config failed - Hardware error\r\n");
        log_write_event(3, NULL); // RTC配置失败
    }

    // 重置配置状态
    g_rtc_config_state = RTC_CONFIG_IDLE;
}

/*************************************************************************************************/
/* Ratio配置功能 */
/*************************************************************************************************/

/**
 * @brief 开始Ratio配置流程
 */
void ratio_config_start(void)
{
    g_ratio_config_state = RATIO_CONFIG_WAITING;
    my_printf(DEBUG_USART, "Ratio=%.2f\r\nInput value(100)\r\n", g_ratio);
    log_write_event(8, NULL); // ratio配置开始
}

/**
 * @brief 设置Ratio值
 * @param value_str 输入的ratio值字符串
 */
void ratio_config_set_value(const char* value_str)
{
    float new_ratio;
    char *endptr;

    // 解析浮点数
    new_ratio = strtof(value_str, &endptr);

    // 检查解析是否成功（endptr指向字符串末尾表示完全解析）
    if (*endptr != '\0') {
        // 解析失败
        my_printf(DEBUG_USART, "ratio invalid\r\nRatio=%.2f\r\n", g_ratio);
        log_write_event(10, NULL); // ratio配置失败
        g_ratio_config_state = RATIO_CONFIG_IDLE;
        return;
    }

    // 检查数值范围（大于0且小于等于100）
    if (new_ratio <= 0.0f || new_ratio > 100.0f) {
        // 数值超出范围
        my_printf(DEBUG_USART, "ratio invalid\r\nRatio=%.2f\r\n", g_ratio);
        log_write_event(10, NULL); // ratio配置失败
        g_ratio_config_state = RATIO_CONFIG_IDLE;
        return;
    }

    // 数值有效，更新ratio变量（只修改内存，不写入Flash）
    g_ratio = new_ratio;
    my_printf(DEBUG_USART, "ratio modified success\r\nRatio=%.2f\r\n", g_ratio);

    // 记录ratio配置成功日志
    char ratio_value[16];
    sprintf(ratio_value, "%.2f", g_ratio);
    log_write_event(9, ratio_value); // ratio配置成功，附加值信息

    // 重置配置状态
    g_ratio_config_state = RATIO_CONFIG_IDLE;
}

/*************************************************************************************************/
/* Limit配置功能 */
/*************************************************************************************************/

/**
 * @brief 开始Limit配置流程
 */
void limit_config_start(void)
{
    g_limit_config_state = LIMIT_CONFIG_WAITING;
    my_printf(DEBUG_USART, "limit=%.2f\r\nInput value(200)\r\n", g_limit);
    log_write_event(11, NULL); // limit配置开始
}

/**
 * @brief 设置Limit值
 * @param value_str 输入的limit值字符串
 */
void limit_config_set_value(const char* value_str)
{
    float new_limit;
    char *endptr;

    // 解析浮点数
    new_limit = strtof(value_str, &endptr);

    // 检查解析是否成功（endptr指向字符串末尾表示完全解析）
    if (*endptr != '\0') {
        // 解析失败
        my_printf(DEBUG_USART, "limit invalid\r\nlimit=%.2f\r\n", g_limit);
        log_write_event(13, NULL); // limit配置失败
        g_limit_config_state = LIMIT_CONFIG_IDLE;
        return;
    }

    // 检查数值范围（大于0且小于等于200）
    if (new_limit <= 0.0f || new_limit > 200.0f) {
        // 数值超出范围
        my_printf(DEBUG_USART, "limit invalid\r\nlimit=%.2f\r\n", g_limit);
        log_write_event(13, NULL); // limit配置失败
        g_limit_config_state = LIMIT_CONFIG_IDLE;
        return;
    }

    // 数值有效，更新limit变量（只修改内存，不写入Flash）
    g_limit = new_limit;
    my_printf(DEBUG_USART, "limit modified success\r\nlimit=%.2f\r\n", g_limit);

    // 记录limit配置成功日志
    char limit_value[16];
    sprintf(limit_value, "%.2f", g_limit);
    log_write_event(12, limit_value); // limit配置成功，附加值信息

    // 重置配置状态
    g_limit_config_state = LIMIT_CONFIG_IDLE;
}

/*************************************************************************************************/
/* 参数保存功能 */
/*************************************************************************************************/

/**
 * @brief 保存当前参数到Flash
 */
void config_save_to_flash(void)
{
    // 显示当前参数值
    my_printf(DEBUG_USART, "ratio:%.2f\r\nlimit:%.2f\r\nsave parameters to flash\r\n",
              g_ratio, g_limit);

    // 调用现有的Flash写入函数保存当前参数
    system_params_write_to_flash();

    // 记录config save日志
    log_write_event(25, NULL); // config save
}

/**
 * @brief 从Flash读取参数并显示
 * 注意：只读取和显示Flash中的值，不修改系统内存中的变量
 */
void config_read_from_flash(void)
{
    system_params_flash_t flash_params;

    // 显示操作提示
    my_printf(DEBUG_USART, "read parameters from flash\r\n");

    // 直接从Flash读取参数（不影响系统内存中的变量）
    spi_flash_buffer_read((uint8_t*)&flash_params, SYSTEM_PARAMS_FLASH_ADDR, sizeof(system_params_flash_t));

    // 显示从Flash中读取的ratio和limit值
    my_printf(DEBUG_USART, "ratio:%.2f\r\nlimit:%.2f\r\n",
              flash_params.ratio, flash_params.limit);

    // 记录config read日志
    log_write_event(26, NULL); // config read
}

/*************************************************************************************************/
/* 文件系统管理功能 */
/*************************************************************************************************/

/**
 * @brief 文件系统初始化并挂载
 */
void filesystem_init(void)
{
    DSTATUS stat = 0;
    FRESULT result = FR_OK;
    uint16_t retry_count = 5;

    // 尝试初始化SD卡
    do {
        stat = disk_initialize(0);
        if (stat != 0) {
            delay_ms(100); // 等待100ms后重试
        }
    } while ((stat != 0) && (--retry_count));

    if (stat == 0) {
        // SD卡初始化成功，尝试挂载文件系统
        result = f_mount(0, &fs); // 挂载SD卡文件系统，设备号0
        // 不输出挂载结果信息
    }
    // 不输出初始化失败信息
}

/**
 * @brief 真正的TF卡测试（包含读写测试）
 * @return 1-测试通过，0-测试失败
 */
uint8_t tf_card_real_test(void)
{
    DSTATUS stat = 0;
    FRESULT result = FR_OK;
    FIL test_file;
    UINT bytes_written, bytes_read;
    char write_data[] = "TF_TEST_DATA_12345";
    char read_data[32] = {0};

    // 1. 检查SD卡初始化状态
    stat = disk_status(0);
    if (stat != 0) {
        return 0; // SD卡未初始化或有错误
    }

    // 2. 尝试创建测试文件
    result = f_open(&test_file, "0:/TEST.TXT", FA_CREATE_ALWAYS | FA_WRITE | FA_READ);
    if (result != FR_OK) {
        return 0; // 无法创建文件
    }

    // 3. 写入测试数据
    result = f_write(&test_file, write_data, strlen(write_data), &bytes_written);
    if (result != FR_OK || bytes_written != strlen(write_data)) {
        f_close(&test_file);
        return 0; // 写入失败
    }

    // 4. 移动文件指针到开头
    f_lseek(&test_file, 0);

    // 5. 读取测试数据
    result = f_read(&test_file, read_data, strlen(write_data), &bytes_read);
    if (result != FR_OK || bytes_read != strlen(write_data)) {
        f_close(&test_file);
        return 0; // 读取失败
    }

    // 6. 比较数据
    if (memcmp(write_data, read_data, strlen(write_data)) != 0) {
        f_close(&test_file);
        return 0; // 数据不匹配
    }

    // 7. 关闭并删除测试文件
    f_close(&test_file);
    f_unlink("0:/TEST.TXT");

    return 1; // 测试通过
}

/**
 * @brief 真正的Flash测试（包含读写测试）
 * @return 1-测试通过，0-测试失败
 */
uint8_t flash_real_test(void)
{
    uint32_t test_addr = FLASH_TEST_ADDR; // 使用专用测试地址，避免与设备ID和系统参数冲突
    uint8_t write_data[64];
    uint8_t read_data[64];
    uint32_t flash_id;

    // 1. 检查Flash ID
    flash_id = spi_flash_read_id();
    if (flash_id == 0x000000 || flash_id == 0xFFFFFF) {
        return 0; // Flash ID无效
    }

    // 2. 准备测试数据
    for (int i = 0; i < 64; i++) {
        write_data[i] = (uint8_t)(0xA5 + i); // 生成测试模式
    }

    // 3. 擦除测试扇区
    spi_flash_sector_erase(test_addr);

    // 4. 验证擦除（应该全为0xFF）
    spi_flash_buffer_read(read_data, test_addr, 64);
    for (int i = 0; i < 64; i++) {
        if (read_data[i] != 0xFF) {
            return 0; // 擦除失败
        }
    }

    // 5. 写入测试数据
    spi_flash_buffer_write(write_data, test_addr, 64);

    // 6. 读取数据
    memset(read_data, 0, 64);
    spi_flash_buffer_read(read_data, test_addr, 64);

    // 7. 比较数据
    if (memcmp(write_data, read_data, 64) != 0) {
        return 0; // 数据不匹配
    }

    // 8. 清理测试区域
    spi_flash_sector_erase(test_addr);

    return 1; // 测试通过
}

/*************************************************************************************************/
/* 系统参数管理功能 */
/*************************************************************************************************/

/**
 * @brief 计算校验和
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验和
 */
static uint32_t calculate_checksum(const uint8_t* data, uint32_t len)
{
    uint32_t checksum = 0;
    for (uint32_t i = 0; i < len; i++) {
        checksum += data[i];
    }
    return checksum;
}



/**
 * @brief 将系统参数写入Flash
 */
void system_params_write_to_flash(void)
{
    system_params_flash_t flash_params;

    // 准备要写入的数据
    flash_params.magic = SYSTEM_PARAMS_MAGIC;
    flash_params.version = SYSTEM_PARAMS_VERSION;
    flash_params.ratio = g_ratio;
    flash_params.limit = g_limit;
    flash_params.period = g_period;
    flash_params.reserved = 0;  // 保留字段设为0

    // 计算校验和
    flash_params.checksum = calculate_checksum((uint8_t*)&flash_params, sizeof(system_params_flash_t) - sizeof(uint32_t));

    // 擦除对应扇区
    spi_flash_sector_erase(SYSTEM_PARAMS_FLASH_ADDR);

    // 写入Flash
    spi_flash_buffer_write((uint8_t*)&flash_params, SYSTEM_PARAMS_FLASH_ADDR, sizeof(system_params_flash_t));
}

/**
 * @brief 系统参数初始化
 * 下载程序时写入指定默认值，正常上电时从Flash读取现有值
 */
void system_params_init(void)
{
    system_params_flash_t flash_params;

    // 运行时检查结构体大小（替代编译时检查）
    if (sizeof(system_params_flash_t) != 24) {
        // 结构体大小不正确，可能是编译器填充导致的
        // 这里可以添加错误处理或警告
        // 暂时继续执行，但需要注意可能的兼容性问题
    }

    // 先尝试从Flash读取
    spi_flash_buffer_read((uint8_t*)&flash_params, SYSTEM_PARAMS_FLASH_ADDR, sizeof(system_params_flash_t));

    // 检查是否为程序下载（版本不匹配表示程序更新）
    if (flash_params.magic != SYSTEM_PARAMS_MAGIC || flash_params.version != SYSTEM_PARAMS_VERSION) {
        // 程序下载时，强制设置指定的默认值并写入Flash
        g_ratio = 0.5f;
        g_limit = 100.0f;
        g_period = 5;

        // 立即写入Flash
        system_params_write_to_flash();
        return;
    }

    // 不是程序下载，验证Flash数据完整性
    uint32_t calculated_checksum = calculate_checksum((uint8_t*)&flash_params, sizeof(system_params_flash_t) - sizeof(uint32_t));
    if (calculated_checksum == flash_params.checksum) {
        // 校验和正确，从Flash读取现有参数（保持Flash中的值不变）
        g_ratio = flash_params.ratio;
        g_limit = flash_params.limit;
        g_period = flash_params.period;
    } else {
        // 校验和错误，Flash数据损坏，使用合理的默认值
        // 先检查Flash中的值是否在合理范围内
        if (flash_params.ratio > 0.0f && flash_params.ratio <= 100.0f) {
            g_ratio = flash_params.ratio;
        } else {
            g_ratio = 0.5f; // 使用默认值
        }

        if (flash_params.limit > 0.0f && flash_params.limit <= 200.0f) {
            g_limit = flash_params.limit;
        } else {
            g_limit = 100.0f; // 使用默认值
        }

        if (flash_params.period > 0 && flash_params.period <= 3600) {
            g_period = flash_params.period;
        } else {
            g_period = 5; // 使用默认值
        }

        // 重新写入Flash以修复损坏的数据
        system_params_write_to_flash();
    }
}

/*************************************************************************************************/
/* 采样模式管理功能 */
/*************************************************************************************************/

// 采样模式相关变量
static uint32_t sampling_start_time = 0;           // 采样模式开始时间
static uint32_t last_dataread_time = 0;            // 上次dataread01事件时间
static uint32_t led1_last_toggle_time = 0;         // LED1上次切换时间
static uint8_t led1_state = 0;                     // LED1当前状态
static uint8_t sampling_mode_initialized = 0;      // 采样模式初始化标志
static uint32_t led2_on_time = 0;                  // LED2点亮时间
static uint8_t led2_auto_off_flag = 0;             // LED2自动熄灭标志
static uint32_t last_rtc_display_time = 0;         // 上次RTC显示更新时间
static uint8_t first_dataread_done = 0;            // 第一次dataread01事件是否已完成

// 数据记录相关变量
static FIL data_file;                              // 当前数据文件对象
static FIL hide_file;                              // 隐藏数据文件对象
static FIL overlimit_file;                         // 超限数据文件对象
static uint8_t file_is_open = 0;                   // 普通数据文件是否已打开标志
static uint8_t hide_file_is_open = 0;              // 隐藏数据文件是否已打开标志
static uint8_t overlimit_file_is_open = 0;         // 超限数据文件是否已打开标志
static uint16_t current_record_count = 0;          // 当前普通文件中的记录数
static uint16_t hide_record_count = 0;             // 当前隐藏文件中的记录数
static uint16_t overlimit_record_count = 0;        // 当前超限文件中的记录数
// 注意：first_dataread_in_session变量已删除，因为串口输出已移至sampling_mode_enter函数
static uint8_t overlimit_output_flag = 0;          // 超限输出标志，控制当次串口输出格式

/*************************************************************************************************/
/* 日志系统相关变量 */
/*************************************************************************************************/
static FIL log_file;                               // 日志文件对象
static uint8_t log_file_is_open = 0;               // 日志文件是否已打开标志
static uint32_t boot_count = 0;                    // 上电次数计数器

/*************************************************************************************************
 * 日志事件名称数组配置说明
 *
 * 这个数组控制所有日志事件的输出内容，您可以根据需要自由修改：
 *
 * 1. 修改事件名称：直接修改双引号内的文字内容
 * 2. 禁用某个事件：将对应位置的字符串改为 0 （数字零）
 * 3. 启用某个事件：将对应位置的 0 改为字符串
 *
 * 注意事项：
 * - 不要修改数组的索引顺序和数量
 * - 字符串长度建议不超过50个字符
 * - 最后一行的 0 是数组结束标志，不要删除
 * - 修改后需要重新编译程序
 *
 * 事件触发位置说明：
 * 索引0-7：   系统级事件（开机、测试等）
 * 索引8-13：  配置事件（ratio、limit等参数配置）
 * 索引14-16： 串口命令启动采样事件
 * 索引17-21： 按键操作事件
 * 索引22-23： hide模式切换事件
 *************************************************************************************************/
static const char* log_event_names[] = {
    // === 系统级事件 (索引0-7) ===
    "system init",                    // 0 - 系统上电初始化（触发位置：main.c系统启动时）
    "rtc config",                     // 1 - RTC配置开始（触发位置：串口输入"RTC Config"）
    "rtc config success to",          // 2 - RTC配置成功（触发位置：RTC时间设置成功后，会追加设置的时间）
    "rtc config failed",              // 3 - RTC配置失败（触发位置：RTC时间设置失败时）
    "system hardware test",           // 4 - 系统硬件测试开始（触发位置：串口输入"test"）
    "test ok",                        // 5 - 测试成功（触发位置：Flash和TF卡测试都通过）
    "test error: tf card not found",  // 6 - TF卡测试失败（触发位置：TF卡读写测试失败）
    "test error: flash not found",    // 7 - Flash测试失败（触发位置：Flash读写测试失败）

    // === 配置事件 (索引8-13) ===
    "ratio config",                   // 8 - ratio配置开始（触发位置：串口输入"ratio"）
    "ratio config success to",        // 9 - ratio配置成功（触发位置：ratio值设置成功后，会追加设置的值）
    "ratio config failed",            // 10 - ratio配置失败（触发位置：ratio值设置失败时）
    "limit config",                   // 11 - limit配置开始（触发位置：串口输入"limit"）
    "limit config success to",        // 12 - limit配置成功（触发位置：limit值设置成功后，会追加设置的值）
    "limit config failed",            // 13 - limit配置失败（触发位置：limit值设置失败时）

    // === 串口命令启动采样事件 (索引14-16) ===
    "sample start - cycle 5s (command)",   // 14 - 串口命令启动采样（5秒周期）（触发位置：串口输入"start"且period=5）
    "sample start - cycle 10s (command)",  // 15 - 串口命令启动采样（10秒周期）（触发位置：串口输入"start"且period=10）
    "sample start - cycle 15s (command)",  // 16 - 串口命令启动采样（15秒周期）（触发位置：串口输入"start"且period=15）

    // === 按键操作事件 (索引17-21) ===
    "sample start -",                 // 17 - 按键启动采样（触发位置：按下KEY1进入采样模式，会追加周期信息）
    "cycle switch to 5s (key press)", // 18 - 按键切换周期到5秒（触发位置：按下KEY2）
    "cycle switch to 10s (key press)",// 19 - 按键切换周期到10秒（触发位置：按下KEY3）
    "cycle switch to 15s (key press)",// 20 - 按键切换周期到15秒（触发位置：按下KEY4）
    "sample stop (key press)",        // 21 - 停止采样（触发位置：按下KEY1退出采样模式）
    "sample stop (command)",          // 22 - 停止采样（触发位置：串口输入"stop"）

    // === hide模式切换事件 (索引23-24) ===
    "hide data",                      // 23 - 进入hide模式（触发位置：在采样模式下串口输入"hide"）
    "unhide data",                    // 24 - 退出hide模式（触发位置：在hide模式下串口输入"unhide"）

    // === 配置保存和读取事件 (索引25-26) ===
    "config save",                    // 25 - 配置保存（触发位置：串口输入"config save"）
    "config read",                    // 26 - 配置读取（触发位置：串口输入"config read"）

    // === 数组结束标志 ===
    0                                 // 27 - 数组结束标志（请勿删除此行）
};

/**
 * @brief 采样模式初始化
 */
void sampling_mode_init(void)
{
    sampling_mode_initialized = 1;
}

/**
 * @brief 进入采样模式
 */
void sampling_mode_enter(void)
{
    // 切换系统状态
    g_system_state = SYSTEM_SAMPLING;

    // 记录进入采样模式的时间
    sampling_start_time = get_system_ms();
    last_dataread_time = 0;  // 重置为0，用于第一次dataread01事件的特殊处理
    led1_last_toggle_time = sampling_start_time;
    last_rtc_display_time = sampling_start_time;

    // 初始化LED1状态为亮
    led1_state = 1;
    ucLed[0] = led1_state;

    // 初始化采样模式数据
    g_adc_01 = 0.0f;
    g_oled_refresh_flag = 0;  // 进入时不立即刷新，等待第一次dataread01事件
    g_rtc_display_flag = 0;   // 不立即显示RTC时间，等待第一次dataread01事件
    first_dataread_done = 0;  // 重置第一次dataread01事件标志

    // 初始化采样模式为普通模式
    g_sampling_mode = SAMPLING_MODE_NORMAL;

    // 立即输出串口信息（不等待第一次dataread01事件）
    my_printf(DEBUG_USART, "Periodic Sampling\r\nsample cycle: %ds\r\n", g_period);

    // 初始化数据记录功能
    data_logging_init();
}

/**
 * @brief 退出采样模式
 */
void sampling_mode_exit(void)
{
    // 串口输出退出提示
    my_printf(DEBUG_USART, "Periodic Sampling STOP\r\n");

    // 切换系统状态
    g_system_state = SYSTEM_IDLE;

    // 关闭LED1
    led1_state = 0;
    ucLed[0] = led1_state;

    // 关闭LED2并重置自动熄灭标志
    ucLed[1] = 0;
    led2_auto_off_flag = 0;

    // 重置显示标志
    g_oled_refresh_flag = 0;
    g_rtc_display_flag = 0;
    first_dataread_done = 0;

    // 关闭数据记录文件
    if (file_is_open) {
        f_close(&data_file);
        file_is_open = 0;
    }

    // 关闭隐藏数据记录文件
    if (hide_file_is_open) {
        f_close(&hide_file);
        hide_file_is_open = 0;
    }

    // 关闭超限数据记录文件
    if (overlimit_file_is_open) {
        f_close(&overlimit_file);
        overlimit_file_is_open = 0;
    }

    // 重置采样模式为普通模式
    g_sampling_mode = SAMPLING_MODE_NORMAL;
}

/**
 * @brief 采样模式任务处理
 * 处理LED1闪烁、定时事件、LED2自动熄灭、RTC显示和overlimit检查
 */
void sampling_mode_task(void)
{
    if (g_system_state != SYSTEM_SAMPLING) {
        // 不在采样模式时，检查LED2自动熄灭
        if (led2_auto_off_flag) {
            uint32_t current_time = get_system_ms();
            if (current_time - led2_on_time >= (g_period * 1000)) {
                ucLed[1] = 0;
                led2_auto_off_flag = 0;
            }
        }
        return; // 不在采样模式，直接返回
    }

    uint32_t current_time = get_system_ms();

    // 1. 处理LED1闪烁（1s周期）
    if (current_time - led1_last_toggle_time >= 1000) {
        led1_state = !led1_state;
        ucLed[0] = led1_state;
        led1_last_toggle_time = current_time;
    }

    // 2. 处理LED2自动熄灭（持续period秒）
    if (led2_auto_off_flag) {
        if (current_time - led2_on_time >= (g_period * 1000)) {
            ucLed[1] = 0;
            led2_auto_off_flag = 0;
        }
    }

    // 3. 检查是否需要触发dataread01事件
    // 进入采样模式2秒后第一次触发，之后每period秒触发一次
    uint32_t time_since_start = current_time - sampling_start_time;

    if (!first_dataread_done) {
        // 第一次dataread01事件：进入采样模式period秒后触发
        if (time_since_start >= (g_period * 1000)) {
            dataread01_event_handler();
            last_dataread_time = current_time;
            first_dataread_done = 1;
        }
    } else {
        // 后续dataread01事件：每period秒触发一次
        uint32_t time_since_last_read = current_time - last_dataread_time;
        if (time_since_last_read >= (g_period * 1000)) {
            dataread01_event_handler();
            last_dataread_time = current_time;
        }
    }

    // 4. 处理RTC时间显示（20Hz，每50ms更新一次）
    // 只有在第一次dataread01事件完成后才开始RTC显示
    if (first_dataread_done && (current_time - last_rtc_display_time >= 50)) {
        g_rtc_display_flag = 1;
        last_rtc_display_time = current_time;
    }
}

/**
 * @brief dataread01事件处理函数
 * 读取ADC值并计算ADC_01，检查overlimit，设置OLED刷新标志，串口输出，数据记录
 */
void dataread01_event_handler(void)
{
    rtc_parameter_struct current_time;
    rtc_decimal_time_t decimal_time;

    // 读取ADC值（只在此时读取）
    uint16_t adc_0 = adc_value[0];

    // 计算ADC_01 = 3.3/4096*ADC_0*ratio，保留两位小数（只在此时更新）
    g_adc_01 = (3.3f / 4096.0f) * adc_0 * g_ratio;

    // 获取当前RTC时间用于串口输出和数据记录
    rtc_current_time_get(&current_time);

    // 转换RTC时间为十进制格式
    rtc_bcd_to_decimal(&current_time, &decimal_time);

    // 注意：串口输出"Periodic Sampling"已移至sampling_mode_enter函数中立即执行

    // 检查是否超过阈值（使用计算后的ADC_01值与limit比较）
    // limit是ADC_01的阈值，直接比较计算后的值
    if (g_adc_01 > g_limit) {
        // 触发overlimit01事件（高优先级）
        overlimit01_event_handler();

        // 写入超限数据记录到overLimit文件夹
        overlimit_data_write_record(&current_time, &decimal_time);
    }

    // 根据模式和超限标志输出不同内容
    if (g_sampling_mode == SAMPLING_MODE_NORMAL) {
        // 普通模式：根据超限标志决定输出格式
        if (overlimit_output_flag) {
            // 超限时的输出格式
            my_printf(DEBUG_USART, "%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit(%.2f)!\r\n",
                      decimal_time.year_dec, decimal_time.month_dec, decimal_time.day_dec,
                      decimal_time.hour_dec, decimal_time.minute_dec, decimal_time.second_dec,
                      g_adc_01, g_limit);
        } else {
            // 正常输出格式
            my_printf(DEBUG_USART, "%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV\r\n",
                      decimal_time.year_dec, decimal_time.month_dec, decimal_time.day_dec,
                      decimal_time.hour_dec, decimal_time.minute_dec, decimal_time.second_dec, g_adc_01);
        }
    } else if (g_sampling_mode == SAMPLING_MODE_HIDE) {
        // hide模式：根据超限标志决定输出格式
        char encrypted_data[17]; // 16个字符 + 结束符
        uint32_t unix_timestamp = rtc_to_unix_timestamp(&current_time);
        hide_data_encrypt(unix_timestamp, g_adc_01, encrypted_data);

        if (overlimit_output_flag) {
            // 超限时的输出格式：加密数据后加*
            my_printf(DEBUG_USART, "%s*\r\n", encrypted_data);
        } else {
            // 正常输出格式：纯加密数据
            my_printf(DEBUG_USART, "%s\r\n", encrypted_data);
        }
    }

    // 写入数据记录到TF卡（传递已获取的时间和转换后的十进制时间）
    data_logging_write_record_with_time(&current_time, &decimal_time);

    // 重置超限输出标志（确保只影响当次输出）
    overlimit_output_flag = 0;

    // 设置OLED刷新标志（只在此时设置，确保在ADC_01更新后）
    g_oled_refresh_flag = 1;
}

/**
 * @brief RTC显示任务处理函数
 * 专门处理采样模式下的RTC时间显示，20Hz刷新
 */
void sampling_rtc_display_task(void)
{
    // 只在采样模式下工作
    if (g_system_state != SYSTEM_SAMPLING) {
        return;
    }

    // 检查RTC显示刷新标志
    if (g_rtc_display_flag) {
        // 获取当前RTC时间并显示到OLED第一行
        rtc_current_time_get(&rtc_initpara);
        // 使用足够的空格确保完全覆盖"system idle"字符
        oled_printf(0, 0, "%02x:%02x:%02x        ", rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);

        // 清除RTC显示刷新标志
        g_rtc_display_flag = 0;
    }
}

/**
 * @brief overlimit01事件处理函数
 * ADC超限时的处理
 */
void overlimit01_event_handler(void)
{
    // LED2点亮
    ucLed[1] = 1;

    // 设置LED2自动熄灭标志和时间（非阻塞方式）
    led2_on_time = get_system_ms();
    led2_auto_off_flag = 1;

    // 设置超限输出标志，影响当次dataread01事件的串口输出格式
    overlimit_output_flag = 1;
}

/**
 * @brief 修改period值并保存到Flash
 * @param new_period 新的周期值
 */
void period_change_and_save(uint16_t new_period)
{
    // 更新全局变量
    g_period = new_period;

    // 保存到Flash
    system_params_write_to_flash();
}

/*************************************************************************************************/
/* 数据记录功能 */
/*************************************************************************************************/

/**
 * @brief 数据记录初始化
 */
void data_logging_init(void)
{
    // 重置普通数据记录状态
    file_is_open = 0;
    current_record_count = 0;
    // 注意：first_dataread_in_session变量已删除

    // 重置隐藏数据记录状态
    hide_file_is_open = 0;
    hide_record_count = 0;

    // 重置超限数据记录状态
    overlimit_file_is_open = 0;
    overlimit_record_count = 0;
    overlimit_output_flag = 0;

    // 如果有普通文件打开，先关闭
    if (file_is_open) {
        f_close(&data_file);
        file_is_open = 0;
    }

    // 如果有隐藏文件打开，先关闭
    if (hide_file_is_open) {
        f_close(&hide_file);
        hide_file_is_open = 0;
    }

    // 如果有超限文件打开，先关闭
    if (overlimit_file_is_open) {
        f_close(&overlimit_file);
        overlimit_file_is_open = 0;
    }
}

/**
 * @brief 写入数据记录到TF卡（使用传入的时间参数）
 * @param rtc_time RTC时间结构体指针
 * @param decimal_time 十进制时间结构体指针
 */
void data_logging_write_record_with_time(rtc_parameter_struct* rtc_time, rtc_decimal_time_t* decimal_time)
{
    if (g_sampling_mode == SAMPLING_MODE_NORMAL) {
        // 普通模式：写入sample文件夹
        FRESULT result;
        UINT bytes_written;
        char filename[64];
        char record_buffer[64];

        // 检查是否需要创建新文件
        if (!file_is_open || current_record_count >= 10) {
            // 如果有文件打开，先关闭
            if (file_is_open) {
                f_close(&data_file);
                file_is_open = 0;
            }

            // 创建新文件名：sampleData{datetime}.txt
            sprintf(filename, "0:/sample/sampleData20%02d%02d%02d%02d%02d%02d.txt",
                    decimal_time->year_dec, decimal_time->month_dec, decimal_time->day_dec,
                    decimal_time->hour_dec, decimal_time->minute_dec, decimal_time->second_dec);

            // 创建并打开新文件
            result = f_open(&data_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
            if (result == FR_OK) {
                file_is_open = 1;
                current_record_count = 0;
            } else {
                // 文件创建失败，返回
                return;
            }
        }

        // 准备数据记录：yy-mm-dd hh:mm:ss ADC_01值V
        sprintf(record_buffer, "%02d-%02d-%02d %02d:%02d:%02d %.2fV\r\n",
                decimal_time->year_dec, decimal_time->month_dec, decimal_time->day_dec,
                decimal_time->hour_dec, decimal_time->minute_dec, decimal_time->second_dec, g_adc_01);

        // 写入数据到文件
        result = f_write(&data_file, record_buffer, strlen(record_buffer), &bytes_written);
        if (result == FR_OK && bytes_written == strlen(record_buffer)) {
            current_record_count++;
            // 立即同步到存储设备
            f_sync(&data_file);
        }
    } else if (g_sampling_mode == SAMPLING_MODE_HIDE) {
        // 隐藏模式：写入hideData文件夹
        FRESULT result;
        UINT bytes_written;
        char filename[64];
        char record_buffer[64];
        char encrypted_data[17];

        // 检查是否需要创建新文件
        if (!hide_file_is_open || hide_record_count >= 10) {
            // 如果有文件打开，先关闭
            if (hide_file_is_open) {
                f_close(&hide_file);
                hide_file_is_open = 0;
            }

            // 创建新文件名：hideData{datetime}.txt
            sprintf(filename, "0:/hideData/hideData20%02d%02d%02d%02d%02d%02d.txt",
                    decimal_time->year_dec, decimal_time->month_dec, decimal_time->day_dec,
                    decimal_time->hour_dec, decimal_time->minute_dec, decimal_time->second_dec);

            // 创建并打开新文件
            result = f_open(&hide_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
            if (result == FR_OK) {
                hide_file_is_open = 1;
                hide_record_count = 0;
            } else {
                // 文件创建失败，返回
                return;
            }
        }

        // 写入第一行：原始数据
        sprintf(record_buffer, "%02d-%02d-%02d %02d:%02d:%02d %.2fV\r\n",
                decimal_time->year_dec, decimal_time->month_dec, decimal_time->day_dec,
                decimal_time->hour_dec, decimal_time->minute_dec, decimal_time->second_dec, g_adc_01);
        result = f_write(&hide_file, record_buffer, strlen(record_buffer), &bytes_written);
        if (result != FR_OK || bytes_written != strlen(record_buffer)) {
            return; // 写入失败，返回
        }

        // 生成并写入第二行：加密数据
        uint32_t unix_timestamp = rtc_to_unix_timestamp(rtc_time);
        hide_data_encrypt(unix_timestamp, g_adc_01, encrypted_data);
        sprintf(record_buffer, "hide:%s\r\n", encrypted_data);
        result = f_write(&hide_file, record_buffer, strlen(record_buffer), &bytes_written);
        if (result == FR_OK && bytes_written == strlen(record_buffer)) {
            hide_record_count++; // 两行为一组，计数加1
            // 立即同步到存储设备
            f_sync(&hide_file);
        }
    }
}



/**
 * @brief 写入超限数据记录到overLimit文件夹
 * @param rtc_time RTC时间结构体指针
 * @param decimal_time 十进制时间结构体指针
 */
void overlimit_data_write_record(rtc_parameter_struct* rtc_time, rtc_decimal_time_t* decimal_time)
{
    FRESULT result;
    UINT bytes_written;
    char filename[64];
    char record_buffer[128];

    // 检查是否需要创建新文件
    if (!overlimit_file_is_open || overlimit_record_count >= 10) {
        // 如果有文件打开，先关闭
        if (overlimit_file_is_open) {
            f_close(&overlimit_file);
            overlimit_file_is_open = 0;
        }

        // 创建新文件名：overLimitData{datetime}.txt
        sprintf(filename, "0:/overLimit/overLimitData20%02d%02d%02d%02d%02d%02d.txt",
                decimal_time->year_dec, decimal_time->month_dec, decimal_time->day_dec,
                decimal_time->hour_dec, decimal_time->minute_dec, decimal_time->second_dec);

        // 创建并打开新文件
        result = f_open(&overlimit_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        if (result == FR_OK) {
            overlimit_file_is_open = 1;
            overlimit_record_count = 0;
        } else {
            // 文件创建失败，返回
            return;
        }
    }

    // 准备超限数据记录：yy-mm-dd hh:mm:ss ADC_01值V limit 限制值V
    sprintf(record_buffer, "%02d-%02d-%02d %02d:%02d:%02d %.2fV limit %.2fV\r\n",
            decimal_time->year_dec, decimal_time->month_dec, decimal_time->day_dec,
            decimal_time->hour_dec, decimal_time->minute_dec, decimal_time->second_dec,
            g_adc_01, g_limit);

    // 写入数据到文件
    result = f_write(&overlimit_file, record_buffer, strlen(record_buffer), &bytes_written);
    if (result == FR_OK && bytes_written == strlen(record_buffer)) {
        overlimit_record_count++;
        // 立即同步到存储设备
        f_sync(&overlimit_file);
    }
}

/*************************************************************************************************/
/* RTC时间转换辅助函数 */
/*************************************************************************************************/

/**
 * @brief RTC时间BCD转换为十进制
 * @param rtc_time RTC时间结构体指针
 * @param decimal_time 输出的十进制时间结构体指针
 */
void rtc_bcd_to_decimal(rtc_parameter_struct* rtc_time, rtc_decimal_time_t* decimal_time)
{
    // BCD转换为十进制
    decimal_time->year_dec = ((rtc_time->year >> 4) * 10 + (rtc_time->year & 0x0F));
    decimal_time->day_dec = ((rtc_time->date >> 4) * 10 + (rtc_time->date & 0x0F));
    decimal_time->hour_dec = ((rtc_time->hour >> 4) * 10 + (rtc_time->hour & 0x0F));
    decimal_time->minute_dec = ((rtc_time->minute >> 4) * 10 + (rtc_time->minute & 0x0F));
    decimal_time->second_dec = ((rtc_time->second >> 4) * 10 + (rtc_time->second & 0x0F));

    // 月份转换（RTC库常量转换为数字）
    switch (rtc_time->month) {
        case RTC_JAN: decimal_time->month_dec = 1; break;
        case RTC_FEB: decimal_time->month_dec = 2; break;
        case RTC_MAR: decimal_time->month_dec = 3; break;
        case RTC_APR: decimal_time->month_dec = 4; break;
        case RTC_MAY: decimal_time->month_dec = 5; break;
        case RTC_JUN: decimal_time->month_dec = 6; break;
        case RTC_JUL: decimal_time->month_dec = 7; break;
        case RTC_AUG: decimal_time->month_dec = 8; break;
        case RTC_SEP: decimal_time->month_dec = 9; break;
        case RTC_OCT: decimal_time->month_dec = 10; break;
        case RTC_NOV: decimal_time->month_dec = 11; break;
        case RTC_DEC: decimal_time->month_dec = 12; break;
        default: decimal_time->month_dec = 1; break;
    }
}

/*************************************************************************************************/
/* Hide模式功能 */
/*************************************************************************************************/

/**
 * @brief 进入hide模式
 */
void hide_mode_enter(void)
{
    // 切换到隐藏采样模式
    g_sampling_mode = SAMPLING_MODE_HIDE;

    // 关闭普通数据文件（如果打开）
    if (file_is_open) {
        f_close(&data_file);
        file_is_open = 0;
        current_record_count = 0;
    }

    // 重置隐藏文件状态
    hide_file_is_open = 0;
    hide_record_count = 0;
}

/**
 * @brief 退出hide模式，回到普通read01模式
 */
void hide_mode_exit(void)
{
    // 切换回普通采样模式
    g_sampling_mode = SAMPLING_MODE_NORMAL;

    // 关闭隐藏数据文件（如果打开）
    if (hide_file_is_open) {
        f_close(&hide_file);
        hide_file_is_open = 0;
        hide_record_count = 0;
    }

    // 重置普通文件状态，下次dataread01事件时会创建新的sample文件
    file_is_open = 0;
    current_record_count = 0;

    // 注意：overlimit文件不关闭，因为overlimit功能在普通模式和hide模式下都需要使用
    // overlimit文件的生命周期与整个采样会话相同，只在sampling_mode_exit时关闭
}

/**
 * @brief RTC时间转换为Unix时间戳
 * @param rtc_time RTC时间结构体指针
 * @return Unix时间戳
 */
uint32_t rtc_to_unix_timestamp(rtc_parameter_struct* rtc_time)
{
    // BCD转换为十进制
    uint32_t year = 2000 + ((rtc_time->year >> 4) * 10 + (rtc_time->year & 0x0F));
    uint32_t day = (rtc_time->date >> 4) * 10 + (rtc_time->date & 0x0F);
    uint32_t hour = (rtc_time->hour >> 4) * 10 + (rtc_time->hour & 0x0F);
    uint32_t minute = (rtc_time->minute >> 4) * 10 + (rtc_time->minute & 0x0F);
    uint32_t second = (rtc_time->second >> 4) * 10 + (rtc_time->second & 0x0F);

    // 月份转换（RTC库常量转换为数字）
    uint32_t month;
    switch (rtc_time->month) {
        case RTC_JAN: month = 1; break;
        case RTC_FEB: month = 2; break;
        case RTC_MAR: month = 3; break;
        case RTC_APR: month = 4; break;
        case RTC_MAY: month = 5; break;
        case RTC_JUN: month = 6; break;
        case RTC_JUL: month = 7; break;
        case RTC_AUG: month = 8; break;
        case RTC_SEP: month = 9; break;
        case RTC_OCT: month = 10; break;
        case RTC_NOV: month = 11; break;
        case RTC_DEC: month = 12; break;
        default: month = 1; break;
    }

    // 精确的Unix时间戳计算
    // 计算从1970年1月1日到指定日期的天数
    uint32_t days = 0;

    // 计算年份贡献的天数（从1970年到year-1年）
    for (uint32_t y = 1970; y < year; y++) {
        if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) {
            days += 366; // 闰年
        } else {
            days += 365; // 平年
        }
    }

    // 计算当前年份中月份贡献的天数
    uint32_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // 检查当前年份是否为闰年
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
        days_in_month[1] = 29; // 闰年2月有29天
    }

    // 累加当前年份中前面月份的天数
    for (uint32_t m = 1; m < month; m++) {
        days += days_in_month[m - 1];
    }

    // 加上当前月份中的天数（减1因为day是从1开始的）
    days += (day - 1);

    // 计算Unix时间戳（调整为UTC时间，减去8小时时区偏移）
    uint32_t timestamp = days * 86400 + hour * 3600 + minute * 60 + second;

    // 减去8小时（28800秒）以匹配UTC时间
    if (timestamp >= 28800) {
        timestamp -= 28800;
    }

    return timestamp;
}

/**
 * @brief 数据加密函数
 * @param unix_timestamp Unix时间戳
 * @param adc_value ADC_01值（保留2位小数精度）
 * @param encrypted_data 输出的加密数据字符串（16字符+结束符）
 */
void hide_data_encrypt(uint32_t unix_timestamp, float adc_value, char* encrypted_data)
{
    // 时间戳转换为4字节HEX（高位在前）
    uint8_t timestamp_bytes[4];
    timestamp_bytes[0] = (unix_timestamp >> 24) & 0xFF;
    timestamp_bytes[1] = (unix_timestamp >> 16) & 0xFF;
    timestamp_bytes[2] = (unix_timestamp >> 8) & 0xFF;
    timestamp_bytes[3] = unix_timestamp & 0xFF;

    // 为了保证2位小数精度，先将ADC值乘以100并四舍五入
    uint32_t adc_value_scaled = (uint32_t)(adc_value * 100.0f + 0.5f);

    // 分解为整数部分和小数部分
    uint16_t integer_part = adc_value_scaled / 100;
    uint16_t fractional_part_scaled = adc_value_scaled % 100;

    // 小数部分转换：将0-99的范围精确映射到0-65535
    // 使用更精确的计算：fractional_hex = (fractional_part_scaled * 65536 + 50) / 100
    // 加50是为了四舍五入
    uint32_t fractional_temp = (fractional_part_scaled * 65536UL + 50) / 100;
    uint16_t fractional_hex = (uint16_t)fractional_temp;

    // 整数部分转换为2字节HEX（高位在前）
    uint8_t integer_bytes[2];
    integer_bytes[0] = (integer_part >> 8) & 0xFF;
    integer_bytes[1] = integer_part & 0xFF;

    // 小数部分转换为2字节HEX（高位在前）
    uint8_t fractional_bytes[2];
    fractional_bytes[0] = (fractional_hex >> 8) & 0xFF;
    fractional_bytes[1] = fractional_hex & 0xFF;

    // 组合成16字符的HEX字符串
    sprintf(encrypted_data, "%02X%02X%02X%02X%02X%02X%02X%02X",
            timestamp_bytes[0], timestamp_bytes[1], timestamp_bytes[2], timestamp_bytes[3],
            integer_bytes[0], integer_bytes[1], fractional_bytes[0], fractional_bytes[1]);
}

/*************************************************************************************************/
/* 日志系统功能函数 */
/*************************************************************************************************/

/**
 * @brief 从Flash读取上电次数
 */
void boot_count_read_from_flash(void)
{
    uint32_t flash_boot_count;

    // 从Flash读取上电次数（使用专用地址）
    spi_flash_buffer_read((uint8_t*)&flash_boot_count, BOOT_COUNT_FLASH_ADDR, sizeof(uint32_t));

    // 检查读取的值是否有效（0xFFFFFFFF表示未初始化）
    if (flash_boot_count == 0xFFFFFFFF) {
        boot_count = 0;
    } else {
        boot_count = flash_boot_count;
    }
}

/**
 * @brief 将上电次数写入Flash
 */
void boot_count_write_to_flash(void)
{
    // 擦除扇区
    spi_flash_sector_erase(BOOT_COUNT_FLASH_ADDR);

    // 写入上电次数
    spi_flash_buffer_write((uint8_t*)&boot_count, BOOT_COUNT_FLASH_ADDR, sizeof(uint32_t));
}

/**
 * @brief 上电次数初始化
 */
void boot_count_init(void)
{
    // 从Flash读取上电次数
    boot_count_read_from_flash();

    // 上电次数自增
    boot_count++;

    // 写回Flash
    boot_count_write_to_flash();
}

/**
 * @brief 重置上电次数为0
 */
void boot_count_reset(void)
{
    boot_count = 0;
    boot_count_write_to_flash();
}

/**
 * @brief 日志系统初始化
 */
void log_system_init(void)
{
    FRESULT result;
    char log_filename[64];

    // 初始化上电次数
    boot_count_init();

    // 初始化Flash日志标志位
    flash_log_flag_read();

    // 创建日志文件名：log{id}.txt，id从0开始
    sprintf(log_filename, "0:/log/log%lu.txt", boot_count - 1);

    // 创建并打开日志文件
    result = f_open(&log_file, log_filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        log_file_is_open = 1;

        // 检查是否需要从Flash转存日志
        if (flash_log_flag == 1) {
            // 关闭当前文件，进行Flash到TF卡的转存
            f_close(&log_file);
            log_file_is_open = 0;

            // 执行Flash到TF卡的转存
            flash_log_transfer_to_tf();

            // 重新打开日志文件
            result = f_open(&log_file, log_filename, FA_CREATE_ALWAYS | FA_WRITE);
            if (result == FR_OK) {
                log_file_is_open = 1;
            }
        }

        // 记录系统初始化日志
        if (log_file_is_open) {
            log_write_event(0, NULL); // 系统初始化事件
        }
    } else {
        // TF卡不可用，写入Flash存储
        log_file_is_open = 0;

        // 根据需求5：当处于未插TF状态时，无论标志位状态如何，都清空Flash重新开始记录
        flash_log_init_for_new_session();

        // 记录系统初始化日志到Flash
        flash_log_write_event(0, NULL);
    }
}

/**
 * @brief 写入日志事件
 * @param event_index 事件索引（对应log_event_names数组）
 * @param additional_info 附加信息（可选，用于某些需要追加信息的事件）
 */
void log_write_event(uint8_t event_index, const char* additional_info)
{
    // 检查事件索引是否有效
    if (event_index >= (sizeof(log_event_names) / sizeof(log_event_names[0]) - 1)) {
        return; // 索引超出范围
    }

    // 检查事件名称是否为0（表示不记录该事件）
    if (log_event_names[event_index] == 0) {
        return; // 该事件设置为不记录
    }

    if (log_file_is_open) {
        // TF卡可用，写入TF卡文件
        rtc_parameter_struct current_time;
        rtc_decimal_time_t decimal_time;
        char log_buffer[256];
        UINT bytes_written;
        FRESULT result;

        // 获取当前RTC时间
        rtc_current_time_get(&current_time);
        rtc_bcd_to_decimal(&current_time, &decimal_time);

        // 构建日志记录
        if (additional_info != NULL) {
            // 有附加信息的事件
            sprintf(log_buffer, "%02d-%02d-%02d %02d:%02d:%02d %s %s\r\n",
                    decimal_time.year_dec, decimal_time.month_dec, decimal_time.day_dec,
                    decimal_time.hour_dec, decimal_time.minute_dec, decimal_time.second_dec,
                    log_event_names[event_index], additional_info);
        } else {
            // 普通事件
            sprintf(log_buffer, "%02d-%02d-%02d %02d:%02d:%02d %s\r\n",
                    decimal_time.year_dec, decimal_time.month_dec, decimal_time.day_dec,
                    decimal_time.hour_dec, decimal_time.minute_dec, decimal_time.second_dec,
                    log_event_names[event_index]);
        }

        // 写入日志文件
        result = f_write(&log_file, log_buffer, strlen(log_buffer), &bytes_written);
        if (result == FR_OK) {
            // 立即同步到存储设备
            f_sync(&log_file);
        }
    } else {
        // TF卡不可用，写入Flash存储
        // 直接写入Flash日志（追加模式）
        // 注意：标志位应该在log_system_init中已经设置好了
        flash_log_write_event(event_index, additional_info);
    }
}



/*************************************************************************************************/
/* Config.ini文件读取功能函数 */
/*************************************************************************************************/

/**
 * @brief 解析config.ini文件内容，查找ratio和limit值
 * @param file_content 文件内容缓冲区
 * @param file_size 文件大小
 * @param ratio 输出的ratio值指针
 * @param limit 输出的limit值指针
 * @return 1-解析成功，0-解析失败
 */
uint8_t parse_config_content(const char* file_content, uint32_t file_size, float* ratio, float* limit)
{
    uint8_t found_ratio = 0;
    uint8_t found_limit = 0;
    uint32_t i = 0;
    char line_buffer[128];
    uint8_t line_index = 0;

    // 逐字符解析文件内容
    while (i < file_size) {
        char ch = file_content[i];

        // 如果遇到换行符或到达文件末尾，处理当前行
        if (ch == '\n' || ch == '\r' || i == file_size - 1) {
            if (i == file_size - 1 && ch != '\n' && ch != '\r') {
                line_buffer[line_index] = ch;
                line_index++;
            }

            // 结束当前行
            line_buffer[line_index] = '\0';

            // 查找 "Ch0 = " 模式
            char* ch0_pos = strstr(line_buffer, "Ch0 = ");
            if (ch0_pos != NULL) {
                // 找到Ch0 = ，提取数值
                char* value_start = ch0_pos + 6; // 跳过 "Ch0 = "
                float value = strtof(value_start, NULL);

                // 根据解析状态判断这是ratio还是limit
                if (!found_ratio) {
                    // 还没找到ratio，这应该是ratio值
                    *ratio = value;
                    found_ratio = 1;
                } else if (!found_limit) {
                    // 已找到ratio但还没找到limit，这应该是limit值
                    *limit = value;
                    found_limit = 1;
                }
            }

            // 重置行缓冲区
            line_index = 0;

            // 如果两个值都找到了，可以提前退出
            if (found_ratio && found_limit) {
                break;
            }
        } else {
            // 将字符添加到行缓冲区
            if (line_index < sizeof(line_buffer) - 1) {
                line_buffer[line_index] = ch;
                line_index++;
            }
        }

        i++;
    }

    // 检查是否成功读取到两个值
    return (found_ratio && found_limit) ? 1 : 0;
}

/**
 * @brief 从config.ini文件读取ratio和limit值
 * @param ratio 输出的ratio值指针
 * @param limit 输出的limit值指针
 * @return 1-读取成功，0-文件不存在或读取失败
 */
uint8_t read_config_ini(float* ratio, float* limit)
{
    FRESULT result;
    FIL config_file;
    char file_buffer[512]; // 文件内容缓冲区
    UINT bytes_read;

    // 尝试打开config.ini文件
    result = f_open(&config_file, "0:/config.ini", FA_READ);
    if (result != FR_OK) {
        return 0; // 文件不存在或无法打开
    }

    // 读取整个文件内容
    result = f_read(&config_file, file_buffer, sizeof(file_buffer) - 1, &bytes_read);
    f_close(&config_file);

    if (result != FR_OK || bytes_read == 0) {
        return 0; // 读取失败
    }

    // 确保字符串结束
    file_buffer[bytes_read] = '\0';

    // 解析文件内容
    return parse_config_content(file_buffer, bytes_read, ratio, limit);
}

/**
 * @brief 处理conf命令，从config.ini读取参数并更新到Flash
 */
void config_ini_process(void)
{
    float config_ratio, config_limit;

    // 尝试从config.ini文件读取参数
    if (read_config_ini(&config_ratio, &config_limit)) {
        // 文件存在且读取成功
        system_params_flash_t flash_params;

        // 先读取当前Flash中的数据
        spi_flash_buffer_read((uint8_t*)&flash_params, SYSTEM_PARAMS_FLASH_ADDR, sizeof(system_params_flash_t));

        // 更新Flash中的ratio和limit值（不影响当前系统变量）
        flash_params.magic = SYSTEM_PARAMS_MAGIC;
        flash_params.version = SYSTEM_PARAMS_VERSION;
        flash_params.ratio = config_ratio;
        flash_params.limit = config_limit;
        // 保持period不变（使用Flash中原有的period值）
        // flash_params.period 保持从Flash读取的原值
        flash_params.reserved = 0;

        // 计算校验和
        flash_params.checksum = calculate_checksum((uint8_t*)&flash_params, sizeof(system_params_flash_t) - sizeof(uint32_t));

        // 擦除并写入Flash
        spi_flash_sector_erase(SYSTEM_PARAMS_FLASH_ADDR);
        spi_flash_buffer_write((uint8_t*)&flash_params, SYSTEM_PARAMS_FLASH_ADDR, sizeof(system_params_flash_t));

        // 输出成功信息
        my_printf(DEBUG_USART, "Ratio=%.2f\r\nLimit=%.2f\r\nconfig read success\r\n",
                  config_ratio, config_limit);
    } else {
        // 文件不存在或读取失败
        my_printf(DEBUG_USART, "config.ini file not found.\r\n");
    }
}

/*************************************************************************************************/
/* 从hardware.c移植的全局变量定义 */
/*************************************************************************************************/
uint8_t ucLed[6] = {0,0,0,0,0,0};  // LED 状态数组，初始全部关闭
__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};

/*************************************************************************************************/
/* 从hardware.c移植的外部变量和依赖项 */
/*************************************************************************************************/
extern uint16_t convertarr[CONVERT_NUM];

/*************************************************************************************************/
/* 从hardware.c移植的内部函数声明 */
/*************************************************************************************************/
// 按键功能函数
uint8_t prv_btn_get_state(struct ebtn_btn *btn);
void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt);

// LED功能函数
void led_disp(uint8_t *ucLed);

// SD卡功能函数
ErrStatus memory_compare(uint8_t* src, uint8_t* dst, uint16_t length);
void card_info_get(void);

/*************************************************************************************************/
/* 从hardware.c移植的ADC应用程序 */
/*************************************************************************************************/

void adc_task(void)
{
    convertarr[0] = adc_value[0];
}

/*************************************************************************************************/
/* 从hardware.c移植的按键应用程序 */
/*************************************************************************************************/

/*  消抖时间（毫秒），释放事件的消抖时间（毫秒），有效点击事件的最小按下时间，最大...,
    两次点击之间被认为是连续点击的最大时间，周期性保持活动事件的时间（毫秒），最大连续点击次数 */
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_6, &defaul_ebtn_param),
};

// static ebtn_btn_combo_t btns_combo[] = {
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_0, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_1, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
// };

uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id)
    {
    case USER_BUTTON_0:
        return !KEY1_READ;
    case USER_BUTTON_1:
        return !KEY2_READ;
    case USER_BUTTON_2:
        return !KEY3_READ;
    case USER_BUTTON_3:
        return !KEY4_READ;
    case USER_BUTTON_4:
        return !KEY5_READ;
    case USER_BUTTON_5:
        return !KEY6_READ;
    case USER_BUTTON_6:
        return !KEYW_READ;
    default:
        return 0;
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    if (evt == EBTN_EVT_ONCLICK)
    {
        switch (btn->key_id)
        {
        case USER_BUTTON_0:  // 按键1 - 进入read01采样模式
            if (g_system_state == SYSTEM_IDLE) {
                sampling_mode_enter();
                // 根据当前period值记录相应的日志
                if (g_period == 5) {
                    log_write_event(17, "cycle 5s (key press)"); // 采样开始（按键方式，5秒周期）
                } else if (g_period == 10) {
                    log_write_event(17, "cycle 10s (key press)"); // 采样开始（按键方式，10秒周期）
                } else if (g_period == 15) {
                    log_write_event(17, "cycle 15s (key press)"); // 采样开始（按键方式，15秒周期）
                } else {
                    // 其他周期值，显示实际值
                    char cycle_info[32];
                    sprintf(cycle_info, "cycle %ds (key press)", g_period);
                    log_write_event(17, cycle_info); // 采样开始（按键方式，实际周期）
                }
            } else if (g_system_state == SYSTEM_SAMPLING) {
                sampling_mode_exit();
                log_write_event(21, NULL); // 采样停止（按键）
            }
            break;
        case USER_BUTTON_1:  // 按键2 - 设置period为5
            period_change_and_save(5);
            my_printf(DEBUG_USART, "sample cycle adjust:5s\r\n");
            log_write_event(18, NULL); // 周期切换到5秒（按键）
            break;
        case USER_BUTTON_2:  // 按键3 - 设置period为10
            period_change_and_save(10);
            my_printf(DEBUG_USART, "sample cycle adjust:10s\r\n");
            log_write_event(19, NULL); // 周期切换到10秒（按键）
            break;
        case USER_BUTTON_3:  // 按键4 - 设置period为15
            period_change_and_save(15);
            my_printf(DEBUG_USART, "sample cycle adjust:15s\r\n");
            log_write_event(20, NULL); // 周期切换到15秒（按键）
            break;
        case USER_BUTTON_4:
            LED5_TOGGLE;
            break;
        case USER_BUTTON_5:
            LED6_TOGGLE;
            break;
        case USER_BUTTON_6:
            LED6_TOGGLE;
            break;
        default:
            break;
        }
    }
}

void app_btn_init(void)
{
    // ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);

    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_0);
    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_1);

    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_2);
    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_3);
}

void btn_task(void)
{
    ebtn_process(get_system_ms());
}

/*************************************************************************************************/
/* 从hardware.c移植的LED应用程序 */
/*************************************************************************************************/

/**
 * @brief 显示或关闭Led
 *
 *
 * @param ucLed Led数据存储数组
 */
void led_disp(uint8_t *ucLed)
{
    // 用于记录当前 LED 状态的临时变量
    uint8_t temp = 0x00;
    // 记录之前 LED 状态的变量，用于判断是否需要更新显示
    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {
        // 将LED状态合并到temp变量中，便于后续比较
        if (ucLed[i]) temp |= (1<<i); // 设置第i位为1
    }

    // 仅当当前状态与之前状态不同的时候，才更新显示
    if (temp_old != temp)
    {
        // 根据GPIO初始化配置，设置对应引脚
        LED1_SET((temp & 0x01) ? 1 : 0);
        LED2_SET((temp & 0x02) ? 1 : 0);
        LED3_SET((temp & 0x04) ? 1 : 0);
        LED4_SET((temp & 0x08) ? 1 : 0);
        LED5_SET((temp & 0x10) ? 1 : 0);
        LED6_SET((temp & 0x20) ? 1 : 0);

        // 更新旧状态
        temp_old = temp;
    }
}

/**
 * @brief LED 显示任务函数
 *
 * 每次调用该函数时，LED 灯根据 ucLed 数组中的值来决定是开启还是关闭
 */
void led_task(void)
{
    led_disp(ucLed);
}

/*************************************************************************************************/
/* 从hardware.c移植的OLED应用程序 */
/*************************************************************************************************/

/**
 * @brief	使用类似printf的方式显示字符串，显示6x8的小号ASCII字符
 * @param x  X轴字符位置  范围：0 - 127
 * @param y  Y轴字符位置  范围：0 - 3
 * 例如：oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后的字符串
  va_list arg;      // 定义可变参数
  int len;          // 返回字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到 buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_task(void)
{
    // 根据系统状态显示不同内容
    if (g_system_state == SYSTEM_IDLE) {
        // 系统空闲状态显示
        oled_printf(0, 0, "system idle");
        // 清空其他行的内容
        oled_printf(0, 1, "                ");
        oled_printf(0, 2, "                ");
    } else if (g_system_state == SYSTEM_SAMPLING) {
        // 采样模式显示 - 只在dataread01事件时刷新ADC_01显示
        if (g_oled_refresh_flag) {
            // 第二行显示ADC_01的值（保留两位小数）
            oled_printf(0, 1, "%.2f", g_adc_01);

            // 清空其他行（只显示要求的内容）
            oled_printf(0, 2, "                ");
            oled_printf(0, 3, "                ");

            // 清除刷新标志
            g_oled_refresh_flag = 0;
        }
        // 注意：RTC时间显示由sampling_rtc_display_task独立处理，20Hz刷新
    }
}

/*************************************************************************************************/
/* 从hardware.c移植的RTC应用程序 */
/*************************************************************************************************/

/*!
    \brief      显示当前时间
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void rtc_task(void)
{
    rtc_current_time_get(&rtc_initpara);

    // 根据系统状态决定是否显示时间
    // 在采样模式下不显示任何内容（用户要求只显示第一行时间和第二行ADC_01）
    if (g_system_state == SYSTEM_IDLE) {
        // 系统空闲状态下不显示第三行的ratio、limit、period值（按用户要求删除）
        // 清空第三行内容
        oled_printf(0, 3, "                ");
    }
    // 采样模式下不执行任何OLED操作，避免与oled_task冲突
}

/*************************************************************************************************/
/* 从hardware.c移植的USART应用程序 */
/*************************************************************************************************/

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    // 初始化可变参数列表
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    for(tx_count = 0; tx_count < len; tx_count++){
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }

    return len;
}

void uart_task(void)
{
    if(!rx_flag) return;

    // 去除字符串末尾的换行符和回车符
    char* cmd_str = (char*)uart_dma_buffer;
    int len = strlen(cmd_str);
    while (len > 0 && (cmd_str[len-1] == '\r' || cmd_str[len-1] == '\n')) {
        cmd_str[len-1] = '\0';
        len--;
    }

    // 检查RTC配置状态
    if (g_rtc_config_state == RTC_CONFIG_WAITING) {
        // 正在等待用户输入时间，处理时间设置
        rtc_config_set_time(cmd_str);
    }
    // 检查Ratio配置状态
    else if (g_ratio_config_state == RATIO_CONFIG_WAITING) {
        // 正在等待用户输入ratio值，处理ratio设置
        ratio_config_set_value(cmd_str);
    }
    // 检查Limit配置状态
    else if (g_limit_config_state == LIMIT_CONFIG_WAITING) {
        // 正在等待用户输入limit值，处理limit设置
        limit_config_set_value(cmd_str);
    }
    // 检查是否收到"test"命令
    else if (strcmp(cmd_str, "test") == 0) {
        // 执行系统自检
        system_selftest();
    }
    // 检查是否收到"RTC Config"命令
    else if (strcmp(cmd_str, "RTC Config") == 0) {
        // 开始RTC配置流程
        rtc_config_start();
    }
    // 检查是否收到"RTC now"命令
    else if (strcmp(cmd_str, "RTC now") == 0) {
        // 显示当前RTC时间
        rtc_show_current_time();
    }
    // 检查是否收到"ratio"命令
    else if (strcmp(cmd_str, "ratio") == 0) {
        // 开始ratio配置流程
        ratio_config_start();
    }
    // 检查是否收到"limit"命令
    else if (strcmp(cmd_str, "limit") == 0) {
        // 开始limit配置流程
        limit_config_start();
    }
    // 检查是否收到"config save"命令（支持常见拼写错误）
    else if (strcmp(cmd_str, "config save") == 0 || strcmp(cmd_str, "cofig save") == 0) {
        // 保存当前参数到Flash
        config_save_to_flash();
    }
    // 检查是否收到"config read"命令（支持常见拼写错误）
    else if (strcmp(cmd_str, "config read") == 0 || strcmp(cmd_str, "cofig read") == 0) {
        // 从Flash读取参数并显示
        config_read_from_flash();
    }
    // 检查是否收到"hide"命令（仅在read01模式下有效）
    else if (strcmp(cmd_str, "hide") == 0) {
        // 只有在采样模式下才能进入hide模式
        if (g_system_state == SYSTEM_SAMPLING) {
            hide_mode_enter();
            log_write_event(23, NULL); // 进入hide模式
        }
    }
    // 检查是否收到"unhide"命令（仅在hide01模式下有效）
    else if (strcmp(cmd_str, "unhide") == 0) {
        // 只有在采样模式且为hide模式下才能退出hide模式
        if (g_system_state == SYSTEM_SAMPLING && g_sampling_mode == SAMPLING_MODE_HIDE) {
            hide_mode_exit();
            log_write_event(24, NULL); // 退出hide模式
        }
    }
    // 检查是否收到"start"命令
    else if (strcmp(cmd_str, "start") == 0) {
        // 开启read01模式，和按键1第一次按下一个效果
        if (g_system_state == SYSTEM_IDLE) {
            sampling_mode_enter();
            // 根据当前period值记录相应的日志
            if (g_period == 5) {
                log_write_event(14, NULL); // sample start - cycle 5s (command)
            } else if (g_period == 10) {
                log_write_event(15, NULL); // sample start - cycle 10s (command)
            } else if (g_period == 15) {
                log_write_event(16, NULL); // sample start - cycle 15s (command)
            } else {
                // 其他周期值，使用默认的5s日志
                log_write_event(14, NULL); // sample start - cycle 5s (command)
            }
        }
    }
    // 检查是否收到"stop"命令
    else if (strcmp(cmd_str, "stop") == 0) {
        // 退出read01模式，和按键1第二次按下一个效果
        if (g_system_state == SYSTEM_SAMPLING) {
            sampling_mode_exit();
            log_write_event(22, NULL); // sample stop (command)
        }
    }
    // 检查是否收到"conf"命令
    else if (strcmp(cmd_str, "conf") == 0) {
        // 从config.ini文件读取参数并更新到Flash
        config_ini_process();
    }
    // 检查是否收到"reset"命令
    else if (strcmp(cmd_str, "reset") == 0) {
        // 重置上电次数为0
        boot_count_reset();
        my_printf(DEBUG_USART, "Boot count reset to 0\r\n");
    }
    // 检查是否收到"clean"命令
    else if (strcmp(cmd_str, "clean") == 0) {
        // 清除Flash中的日志内容并重置标志位
        flash_log_clear();

        // 如果当前系统正在Flash模式下运行（没有TF卡），需要重新初始化Flash日志
        if (!log_file_is_open) {
            flash_log_init_for_new_session();
        }

        my_printf(DEBUG_USART, "Flash log cleared\r\n");
    }
    else {
        // 原有的回显功能
        my_printf(DEBUG_USART, "%s", uart_dma_buffer);
    }

    memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
    rx_flag = 0;
}

/*************************************************************************************************/
/* 从hardware.c移植的SD卡应用程序 */
/*************************************************************************************************/

FATFS fs;
FIL fdst;
uint16_t i = 0, count, result = 0;
UINT br, bw;

sd_card_info_struct sd_cardinfo;

BYTE buffer[128];
BYTE filebuffer[128];

ErrStatus memory_compare(uint8_t* src, uint8_t* dst, uint16_t length)
{
    while(length --){
        if(*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

void sd_fatfs_init(void)
{
    nvic_irq_enable(SDIO_IRQn, 0, 0);					// 使能SDIO中断，优先级为0
}

/**
 * @brief       通过串口打印SD卡详细信息
 * @param       无
 * @retval      无
 */
void card_info_get(void)
{
    sd_card_info_struct sd_cardinfo;      // SD卡信息结构体
    sd_error_enum status;                 // SD卡操作状态
    uint32_t block_count, block_size;

    // 获取SD卡信息
    status = sd_card_information_get(&sd_cardinfo);

    if(SD_OK == status)
    {
        my_printf(DEBUG_USART, "\r\n*** SD Card Info ***\r\n");

        // 打印卡类型
        switch(sd_cardinfo.card_type)
        {
            case SDIO_STD_CAPACITY_SD_CARD_V1_1:
                my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V1.1\r\n");
                break;
            case SDIO_STD_CAPACITY_SD_CARD_V2_0:
                my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V2.0\r\n");
                break;
            case SDIO_HIGH_CAPACITY_SD_CARD:
                my_printf(DEBUG_USART, "Card Type: High Capacity SD Card\r\n");
                break;
            case SDIO_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: Multimedia Card\r\n");
                break;
            case SDIO_HIGH_CAPACITY_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: High Capacity Multimedia Card\r\n");
                break;
            case SDIO_HIGH_SPEED_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: High Speed Multimedia Card\r\n");
                break;
            default:
                my_printf(DEBUG_USART, "Card Type: Unknown\r\n");
                break;
        }

        // 打印卡容量和块大小
        block_count = (sd_cardinfo.card_csd.c_size + 1) * 1024;
        block_size = 512;
        my_printf(DEBUG_USART,"\r\n## Device size is %dKB (%.2fGB)##", sd_card_capacity_get(), sd_card_capacity_get() / 1024.0f / 1024.0f);
        my_printf(DEBUG_USART,"\r\n## Block size is %dB ##", block_size);
        my_printf(DEBUG_USART,"\r\n## Block count is %d ##", block_count);

        // 打印制造商ID和产品信息
        my_printf(DEBUG_USART, "Manufacturer ID: 0x%X\r\n", sd_cardinfo.card_cid.mid);
        my_printf(DEBUG_USART, "OEM/Application ID: 0x%X\r\n", sd_cardinfo.card_cid.oid);

        // 打印产品名称 (PNM)
        uint8_t pnm[6];
        pnm[0] = (sd_cardinfo.card_cid.pnm0 >> 24) & 0xFF;
        pnm[1] = (sd_cardinfo.card_cid.pnm0 >> 16) & 0xFF;
        pnm[2] = (sd_cardinfo.card_cid.pnm0 >> 8) & 0xFF;
        pnm[3] = sd_cardinfo.card_cid.pnm0 & 0xFF;
        pnm[4] = sd_cardinfo.card_cid.pnm1 & 0xFF;
        pnm[5] = '\0';
        my_printf(DEBUG_USART, "Product Name: %s\r\n", pnm);

        // 打印产品版本和序列号
        my_printf(DEBUG_USART, "Product Revision: %d.%d\r\n", (sd_cardinfo.card_cid.prv >> 4) & 0x0F, sd_cardinfo.card_cid.prv & 0x0F);
        // 序列号可能无法正确显示，这里附加
        my_printf(DEBUG_USART, "Product Serial Number: 0x%08X\r\n", sd_cardinfo.card_cid.psn);

        // 打印CSD版本和其他CSD信息
        my_printf(DEBUG_USART, "CSD Version: %d.0\r\n", sd_cardinfo.card_csd.csd_struct + 1);

    }
    else
    {
        my_printf(DEBUG_USART, "\r\nFailed to get SD card information, error code: %d\r\n", status);
    }
}

/*************************************************************************************************/
/* Flash日志存储功能函数 */
/*************************************************************************************************/

/**
 * @brief 从Flash读取日志标志位并恢复写入偏移量
 */
void flash_log_flag_read(void)
{
    uint8_t flag_data[4] = {0};

    // 从Flash读取标志位数据
    spi_flash_buffer_read(flag_data, FLASH_LOG_FLAG_ADDR, 4);

    // 检查读取的值是否有效（0xFFFFFFFF表示未初始化）
    if (flag_data[0] == 0xFF && flag_data[1] == 0xFF &&
        flag_data[2] == 0xFF && flag_data[3] == 0xFF) {
        flash_log_flag = 0;
        flash_log_write_offset = 0;
    } else {
        flash_log_flag = flag_data[0];

        // 如果标志位为1，需要恢复写入偏移量
        if (flash_log_flag == 1) {
            // 读取Flash中的数据来计算当前的写入偏移量
            static uint8_t check_buffer[512]; // 减小缓冲区，分批读取
            flash_log_write_offset = 0;

            // 分批读取Flash数据，查找第一个0xFF字节
            for (uint32_t offset = 0; offset < FLASH_LOG_MAX_SIZE; offset += sizeof(check_buffer)) {
                uint32_t read_size = (offset + sizeof(check_buffer) > FLASH_LOG_MAX_SIZE) ?
                                   (FLASH_LOG_MAX_SIZE - offset) : sizeof(check_buffer);

                spi_flash_buffer_read(check_buffer, FLASH_LOG_DATA_ADDR + offset, read_size);

                // 在当前块中查找0xFF
                for (uint32_t i = 0; i < read_size; i++) {
                    if (check_buffer[i] == 0xFF) {
                        flash_log_write_offset = offset + i;
                        goto offset_found;
                    }
                }
            }

            // 如果没有找到0xFF，说明Flash已满
            flash_log_write_offset = FLASH_LOG_MAX_SIZE;

            offset_found:;
        } else {
            flash_log_write_offset = 0;
        }
    }
}

/**
 * @brief 初始化Flash日志存储（清空并设置标志位为1）
 */
void flash_log_init_for_new_session(void)
{
    uint8_t flag_data[4] = {0};
    flag_data[0] = 1;

    // 擦除整个日志存储扇区
    spi_flash_sector_erase(FLASH_LOG_ADDR);

    // 写入标志位
    spi_flash_buffer_write(flag_data, FLASH_LOG_FLAG_ADDR, 4);

    // 重置全局变量
    flash_log_flag = 1;
    flash_log_write_offset = 0;
}

/**
 * @brief 重置Flash日志标志位为0（用于转存完成后）
 */
void flash_log_reset_flag(void)
{
    uint8_t flag_data[4] = {0};
    flag_data[0] = 0;

    // 擦除整个日志存储扇区
    spi_flash_sector_erase(FLASH_LOG_ADDR);

    // 写入标志位
    spi_flash_buffer_write(flag_data, FLASH_LOG_FLAG_ADDR, 4);

    // 重置全局变量
    flash_log_flag = 0;
    flash_log_write_offset = 0;
}

/**
 * @brief 清除Flash中的日志内容（用于clean命令）
 */
void flash_log_clear(void)
{
    // 擦除整个日志存储扇区
    spi_flash_sector_erase(FLASH_LOG_ADDR);

    // 重置标志位和偏移量
    flash_log_flag = 0;
    flash_log_write_offset = 0;
}

/**
 * @brief 向Flash写入日志事件
 * @param event_index 事件索引
 * @param additional_info 附加信息
 */
void flash_log_write_event(uint8_t event_index, const char* additional_info)
{
    // 检查Flash日志标志位是否有效
    if (flash_log_flag != 1) {
        return; // 标志位无效，不写入Flash
    }

    // 检查事件索引是否有效
    if (event_index >= (sizeof(log_event_names) / sizeof(log_event_names[0]) - 1)) {
        return; // 索引超出范围
    }

    // 检查事件名称是否为0（表示不记录该事件）
    if (log_event_names[event_index] == 0) {
        return; // 该事件设置为不记录
    }

    rtc_parameter_struct current_time;
    rtc_decimal_time_t decimal_time;
    char log_buffer[256];
    uint32_t log_length;

    // 获取当前RTC时间
    rtc_current_time_get(&current_time);
    rtc_bcd_to_decimal(&current_time, &decimal_time);

    // 构建日志记录（与TF卡日志格式完全相同）
    if (additional_info != NULL) {
        // 有附加信息的事件
        sprintf(log_buffer, "%02d-%02d-%02d %02d:%02d:%02d %s %s\r\n",
                decimal_time.year_dec, decimal_time.month_dec, decimal_time.day_dec,
                decimal_time.hour_dec, decimal_time.minute_dec, decimal_time.second_dec,
                log_event_names[event_index], additional_info);
    } else {
        // 普通事件
        sprintf(log_buffer, "%02d-%02d-%02d %02d:%02d:%02d %s\r\n",
                decimal_time.year_dec, decimal_time.month_dec, decimal_time.day_dec,
                decimal_time.hour_dec, decimal_time.minute_dec, decimal_time.second_dec,
                log_event_names[event_index]);
    }

    log_length = strlen(log_buffer);

    // 检查是否有足够空间存储
    if (flash_log_write_offset + log_length >= FLASH_LOG_MAX_SIZE) {
        // 空间不足，不写入
        return;
    }

    // 写入日志到Flash
    spi_flash_buffer_write((uint8_t*)log_buffer,
                          FLASH_LOG_DATA_ADDR + flash_log_write_offset,
                          log_length);

    // 验证写入是否成功（读回验证）
    static uint8_t verify_buffer[256];
    if (log_length <= sizeof(verify_buffer)) {
        spi_flash_buffer_read(verify_buffer, FLASH_LOG_DATA_ADDR + flash_log_write_offset, log_length);

        // 比较写入的数据和读回的数据
        uint8_t write_success = 1;
        for (uint32_t i = 0; i < log_length; i++) {
            if (verify_buffer[i] != ((uint8_t*)log_buffer)[i]) {
                write_success = 0;
                break;
            }
        }

        // 只有写入成功才更新偏移量
        if (write_success) {
            flash_log_write_offset += log_length;
        }
    } else {
        // 日志过长，无法验证，直接更新偏移量
        flash_log_write_offset += log_length;
    }
}

/**
 * @brief 将Flash中的日志转存到TF卡
 */
void flash_log_transfer_to_tf(void)
{
    if (flash_log_flag != 1) {
        return; // 标志位不为1，无需转存
    }

    // 使用当前的写入偏移量作为数据长度（更准确且高效）
    uint32_t data_length = flash_log_write_offset;

    // 读取Flash中的有效日志数据
    static uint8_t transfer_buffer[FLASH_LOG_MAX_SIZE];
    if (data_length > 0 && data_length <= FLASH_LOG_MAX_SIZE) {
        spi_flash_buffer_read(transfer_buffer, FLASH_LOG_DATA_ADDR, data_length);
    }

    if (data_length == 0) {
        // 没有有效数据，直接重置标志位
        flash_log_reset_flag();
        return;
    }

    // 创建日志文件名：使用上一次的上电次数
    // 当前boot_count已经自增，所以上一次的文件序号是boot_count-2
    // 但最小值是0（第一个日志文件是log0.txt）
    char log_filename[64];
    uint32_t log_file_index = (boot_count >= 2) ? (boot_count - 2) : 0;
    sprintf(log_filename, "0:/log/log%lu.txt", log_file_index);

    // 使用独立的文件句柄，避免与全局log_file冲突
    FIL transfer_file;
    FRESULT result = f_open(&transfer_file, log_filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (result == FR_OK) {
        UINT bytes_written;

        // 将Flash中的日志数据写入TF卡文件
        result = f_write(&transfer_file, transfer_buffer, data_length, &bytes_written);
        if (result == FR_OK && bytes_written == data_length) {
            // 同步到存储设备
            f_sync(&transfer_file);

            // 关闭文件
            f_close(&transfer_file);

            // 转存成功，重置标志位
            flash_log_reset_flag();
        } else {
            // 写入失败，关闭文件但不重置标志位（保留Flash数据以便下次重试）
            f_close(&transfer_file);
        }
    }
    // 如果文件打开失败，不重置标志位（保留Flash数据以便下次重试）
}




